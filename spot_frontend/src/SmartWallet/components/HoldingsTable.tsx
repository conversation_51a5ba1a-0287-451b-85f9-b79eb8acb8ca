import React, { useState, useEffect } from 'react';
import { FaSpinner } from 'react-icons/fa';
import { HoldingsData } from '../SmartWalletDetail';

interface HoldingsTableProps {
  walletAddress: string;
}

const HoldingsTable: React.FC<HoldingsTableProps> = ({ walletAddress }) => {
  const [data, setData] = useState<HoldingsData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const formatNumber = (num: number, decimals: number = 2): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toFixed(decimals);
  };

  const formatPnL = (value: number): { text: string; color: string } => {
    const isPositive = value >= 0;
    const formattedValue = `${isPositive ? '+' : ''}$${formatNumber(Math.abs(value))}`;
    return {
      text: formattedValue,
      color: isPositive ? 'text-green-400' : 'text-red-400'
    };
  };

  // Mock data for demonstration
  const mockData: HoldingsData[] = [
    {
      id: '1',
      tokenSymbol: 'WIF',
      tokenName: 'dogwifhat',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '2h',
      unrealized: 1245.67,
      totalProfit: 3371.24,
      balance: 125.5,
      position: 15.2,
      bought: 850.33,
      sold: 1096.0,
      transactions30d: 8
    },
    {
      id: '2',
      tokenSymbol: 'BONK',
      tokenName: 'Bonk',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '4h',
      unrealized: 567.89,
      totalProfit: 1234.56,
      balance: 45000.0,
      position: 8.7,
      bought: 432.11,
      sold: 666.67,
      transactions30d: 12
    },
    {
      id: '3',
      tokenSymbol: 'GOAT',
      tokenName: 'Goatseus Maximus',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '6h',
      unrealized: 2134.56,
      totalProfit: 4567.89,
      balance: 89.3,
      position: 22.1,
      bought: 1200.0,
      sold: 2433.33,
      transactions30d: 15
    },
    {
      id: '4',
      tokenSymbol: 'POPCAT',
      tokenName: 'Popcat',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '8h',
      unrealized: 789.12,
      totalProfit: 1567.89,
      balance: 234.5,
      position: 12.4,
      bought: 345.67,
      sold: 778.77,
      transactions30d: 6
    },
    {
      id: '5',
      tokenSymbol: 'MOODENG',
      tokenName: 'Moo Deng',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '12h',
      unrealized: 345.67,
      totalProfit: 890.12,
      balance: 67.8,
      position: 9.8,
      bought: 456.78,
      sold: 544.45,
      transactions30d: 4
    },
    {
      id: '6',
      tokenSymbol: 'PNUT',
      tokenName: 'Peanut the Squirrel',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '1d',
      unrealized: -156.78,
      totalProfit: 234.56,
      balance: 1234.5,
      position: 5.6,
      bought: 678.90,
      sold: 391.34,
      transactions30d: 3
    },
    {
      id: '7',
      tokenSymbol: 'FWOG',
      tokenName: 'FWOG',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '1d',
      unrealized: 456.78,
      totalProfit: 1123.45,
      balance: 156.7,
      position: 7.9,
      bought: 567.89,
      sold: 666.67,
      transactions30d: 9
    },
    {
      id: '8',
      tokenSymbol: 'CHILLGUY',
      tokenName: 'Just a chill guy',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '2d',
      unrealized: 123.45,
      totalProfit: 567.89,
      balance: 78.9,
      position: 4.2,
      bought: 234.56,
      sold: 444.44,
      transactions30d: 2
    },
    {
      id: '9',
      tokenSymbol: 'PONKE',
      tokenName: 'Ponke',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '2d',
      unrealized: 678.90,
      totalProfit: 1789.12,
      balance: 345.6,
      position: 13.7,
      bought: 890.12,
      sold: 1110.22,
      transactions30d: 11
    },
    {
      id: '10',
      tokenSymbol: 'GIGA',
      tokenName: 'GIGA',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '3d',
      unrealized: 234.56,
      totalProfit: 678.90,
      balance: 123.4,
      position: 6.8,
      bought: 345.67,
      sold: 444.34,
      transactions30d: 5
    },
    {
      id: '11',
      tokenSymbol: 'MICHI',
      tokenName: 'Michi',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '4d',
      unrealized: 1234.56,
      totalProfit: 2890.12,
      balance: 567.8,
      position: 18.9,
      bought: 1234.56,
      sold: 1655.56,
      transactions30d: 14
    },
    {
      id: '12',
      tokenSymbol: 'SLERF',
      tokenName: 'Slerf',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '5d',
      unrealized: -89.12,
      totalProfit: 345.67,
      balance: 234.5,
      position: 3.4,
      bought: 456.78,
      sold: 434.79,
      transactions30d: 2
    },
    {
      id: '13',
      tokenSymbol: 'MYRO',
      tokenName: 'Myro',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '6d',
      unrealized: 567.89,
      totalProfit: 1234.56,
      balance: 345.6,
      position: 11.2,
      bought: 678.90,
      sold: 666.67,
      transactions30d: 7
    },
    {
      id: '14',
      tokenSymbol: 'BOME',
      tokenName: 'BOOK OF MEME',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '1w',
      unrealized: -234.56,
      totalProfit: 123.45,
      balance: 89.3,
      position: 2.1,
      bought: 567.89,
      sold: 357.78,
      transactions30d: 3
    },
    {
      id: '15',
      tokenSymbol: 'MEW',
      tokenName: 'cat in a dogs world',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '1w',
      unrealized: 789.12,
      totalProfit: 1567.89,
      balance: 234.5,
      position: 14.6,
      bought: 890.12,
      sold: 778.77,
      transactions30d: 10
    },
    {
      id: '16',
      tokenSymbol: 'RETARDIO',
      tokenName: 'Retardio',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '1w',
      unrealized: 123.45,
      totalProfit: 456.78,
      balance: 67.8,
      position: 5.9,
      bought: 234.56,
      sold: 333.33,
      transactions30d: 1
    },
    {
      id: '17',
      tokenSymbol: 'PEPE',
      tokenName: 'Pepe',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '2w',
      unrealized: 345.67,
      totalProfit: 890.12,
      balance: 156.7,
      position: 8.3,
      bought: 456.78,
      sold: 544.45,
      transactions30d: 6
    },
    {
      id: '18',
      tokenSymbol: 'TRUMP',
      tokenName: 'TRUMP',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '2w',
      unrealized: -67.89,
      totalProfit: 234.56,
      balance: 45.2,
      position: 1.8,
      bought: 345.67,
      sold: 302.45,
      transactions30d: 2
    }
  ];

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setData(mockData);
      } catch (error) {
        console.error('Error fetching holdings data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [walletAddress]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex items-center">
          <FaSpinner className="animate-spin text-[#7FFFD4] text-2xl mr-3" />
          <span className="text-white text-lg">Loading holdings data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Fixed Header */}
      <div className="bg-[#1A1E24] border-b border-gray-700/50 px-4 sm:px-6 py-4">
        <div className="grid grid-cols-8 gap-2 sm:gap-4 text-xs font-medium text-gray-400 uppercase tracking-wider">
          <div className="col-span-2">Token/Last Active</div>
          <div className="text-right hidden sm:block">Unrealized</div>
          <div className="text-right">Total Profit</div>
          <div className="text-right hidden md:block">Balance</div>
          <div className="text-right hidden lg:block">Position</div>
          <div className="text-right hidden md:block">Bought</div>
          <div className="text-right hidden md:block">Sold</div>
          <div className="text-right">30D TXs</div>
        </div>
      </div>

      {/* Scrollable Body */}
      <div className="flex-1 overflow-y-auto custom-scrollbar">
        {data.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="text-gray-400 text-lg mb-2">No holdings data</div>
              <div className="text-gray-500 text-sm">No current holdings found for this wallet</div>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-800/50">
            {data.map((item) => {
              const unrealized = formatPnL(item.unrealized);
              const totalProfit = formatPnL(item.totalProfit);

              return (
                <div key={item.id} className="px-4 sm:px-6 py-4 hover:bg-[#1F2329] transition-colors">
                  <div className="grid grid-cols-8 gap-2 sm:gap-4 items-center">
                    {/* Token/Last Active */}
                    <div className="col-span-2 flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-[#7FFFD4] to-[#025FDA] rounded-full flex items-center justify-center">
                        <span className="text-black text-xs font-bold">
                          {item.tokenSymbol.slice(0, 2)}
                        </span>
                      </div>
                      <div>
                        <div className="text-white font-medium text-sm">{item.tokenSymbol}</div>
                        <div className="text-gray-400 text-xs">{item.lastActive}</div>
                      </div>
                    </div>

                    {/* Unrealized */}
                    <div className={`text-right font-medium text-sm ${unrealized.color}`}>
                      {unrealized.text}
                    </div>

                    {/* Total Profit */}
                    <div className={`text-right font-medium text-sm ${totalProfit.color}`}>
                      {totalProfit.text}
                    </div>

                    {/* Balance */}
                    <div className="text-right text-white text-sm">
                      ${formatNumber(item.balance)}
                    </div>

                    {/* Position */}
                    <div className="text-right text-white text-sm">
                      {item.position}%
                    </div>

                    {/* Bought */}
                    <div className="text-right text-white text-sm">
                      ${formatNumber(item.bought)}
                    </div>

                    {/* Sold */}
                    <div className="text-right text-white text-sm">
                      ${formatNumber(item.sold)}
                    </div>

                    {/* 30D TXs */}
                    <div className="text-right text-gray-400 text-sm">
                      {item.transactions30d}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default HoldingsTable;

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import Navbar from '@/Home/Navbar/Navbar';
import WalletOverviewHeader from './components/WalletOverviewHeader';
import WalletStatistics from './components/WalletStatistics';
import WalletDetailTabs from './components/WalletDetailTabs';

export interface WalletDetailData {
  walletAddress: string;
  solBalance: number;
  totalPnL: number;
  last7DPnL: number;
  winRate: number;
  volume7D: number;
  unrealizedProfits: number;
  lastActivity: string;
  category: string;
  // Additional metrics for detail view
  totalTrades: number;
  successfulTrades: number;
  avgHoldTime: string;
  largestWin: number;
  largestLoss: number;
  profitFactor: number;
}

export interface RecentPnLData {
  id: string;
  tokenSymbol: string;
  tokenName: string;
  tokenImage?: string;
  lastActive: string;
  unrealized: number;
  realizedProfit: number;
  totalProfit: number;
  balance: number;
  position: number;
  bought: number;
  sold: number;
  transactions30d: number;
}

export interface HoldingsData {
  id: string;
  tokenSymbol: string;
  tokenName: string;
  tokenImage?: string;
  lastActive: string;
  unrealized: number;
  totalProfit: number;
  balance: number;
  position: number;
  bought: number;
  sold: number;
  transactions30d: number;
}

export interface ActivityData {
  id: string;
  activity: string;
  type: 'Buy' | 'Sell';
  tokenSymbol: string;
  tokenName: string;
  tokenImage?: string;
  total: number;
  amount: number;
  price: number;
  profit: number;
  duration: string;
  copyTradeHash?: string;
  timestamp: string;
}

const SmartWalletDetail: React.FC = () => {
  const { walletAddress } = useParams<{ walletAddress: string }>();
  const navigate = useNavigate();
  const [walletData, setWalletData] = useState<WalletDetailData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Mock data for demonstration - replace with actual API calls
  const mockWalletData: WalletDetailData = {
    walletAddress: walletAddress || '',
    solBalance: 2500.7,
    totalPnL: 3371.15,
    last7DPnL: 897.7,
    winRate: 62.99,
    volume7D: 3.3,
    unrealizedProfits: 0,
    lastActivity: '7h ago',
    category: 'Pump Smart Money',
    totalTrades: 157,
    successfulTrades: 99,
    avgHoldTime: '2.5h',
    largestWin: 1250.45,
    largestLoss: -89.32,
    profitFactor: 4.2
  };

  useEffect(() => {
    const fetchWalletData = async () => {
      if (!walletAddress) {
        setError('No wallet address provided');
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // In a real implementation, you would fetch data based on walletAddress
        // const response = await fetch(`/api/smart-wallet/${walletAddress}`);
        // const data = await response.json();
        
        setWalletData(mockWalletData);
      } catch (err) {
        console.error('Error fetching wallet data:', err);
        setError('Failed to load wallet data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchWalletData();
  }, [walletAddress]);

  const handleBackClick = () => {
    navigate('/smart-wallet');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col bg-[#141416]">
        <Navbar />
        <main className="flex-1 flex items-center justify-center">
          <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-12 border border-gray-800/50">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#7FFFD4] mr-4"></div>
              <span className="text-white text-lg">Loading wallet details...</span>
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (error || !walletData) {
    return (
      <div className="min-h-screen flex flex-col bg-[#141416]">
        <Navbar />
        <main className="flex-1 flex items-center justify-center">
          <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-12 border border-gray-800/50 text-center">
            <div className="text-red-400 text-lg mb-4">{error || 'Wallet not found'}</div>
            <button
              onClick={handleBackClick}
              className="px-4 py-2 bg-[#7FFFD4] text-black rounded-lg hover:bg-[#6EEEC4] transition-colors"
            >
              Back to Smart Wallets
            </button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-[#141416]">
      <Navbar />

      <main className="flex-1 overflow-hidden">
        <div className="w-full px-4 sm:px-6 lg:px-8 h-full">
          <div className="py-4 sm:py-6 h-full w-full flex flex-col">
            {/* Back Button and Header */}
            <div className="mb-4 sm:mb-6">
              <button
                onClick={handleBackClick}
                className="flex items-center text-gray-400 hover:text-white transition-colors mb-4"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Smart Wallets
              </button>
              <h1 className="text-xl sm:text-2xl font-bold text-white mb-2">Wallet Details</h1>
              <p className="text-gray-400 text-sm">Detailed analysis for {walletAddress}</p>
            </div>

            {/* Wallet Overview Header */}
            <div className="mb-4 sm:mb-6 flex-shrink-0">
              <WalletOverviewHeader walletData={walletData} />
            </div>

            {/* Wallet Statistics */}
            <div className="mb-4 sm:mb-6 flex-shrink-0">
              <WalletStatistics walletData={walletData} />
            </div>

            {/* Wallet Detail Tables with Tabs */}
            <div className="flex-1 min-h-0 overflow-hidden">
              <WalletDetailTabs walletAddress={walletAddress} />
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default SmartWalletDetail;
